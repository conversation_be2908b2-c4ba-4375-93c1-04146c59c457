import { Sender } from '../app/utils/sender/sender'

async function debugTest() {
    console.log('🔍 Debug test bắt đầu...')
    
    // Test với một URL không tồn tại để xem timeout có hoạt động không
    console.log('📡 Tạo Sender với timeout 2 giây...')
    const sender = new Sender('http://localhost:99999/test', { 
        timeout: 2000
    })
    
    console.log('🚀 Gửi request...')
    const startTime = Date.now()
    
    try {
        const result = await sender.send('test')
        console.log('❌ Request thành công (không mong đợi):', result)
    } catch (error) {
        const duration = Date.now() - startTime
        console.log(`⏱️  Request failed sau ${duration}ms`)
        console.log('🔍 Error details:')
        console.log('  - Type:', error?.constructor?.name)
        console.log('  - Message:', error?.message)
        console.log('  - Cause type:', error?.cause?.constructor?.name)
        console.log('  - Cause name:', error?.cause?.name)
        console.log('  - Cause message:', error?.cause?.message)
        
        if (duration >= 1800 && duration <= 2500) {
            console.log('✅ Timeout hoạt động đúng!')
        } else {
            console.log('❌ Timeout không hoạt động đúng')
        }
    }
}

debugTest().catch(console.error)
