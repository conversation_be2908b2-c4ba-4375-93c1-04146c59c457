import { Sender } from '../app/utils/sender/sender'
import { SenderRequestError } from '../app/utils/sender/errors/sender-request-error'
import { createServer, type Server } from 'http'

class TestRunner {
    private server: Server | null = null
    private port = 0

    async setup() {
        console.log('🚀 Khởi tạo test server...')
        
        this.server = createServer((req, res) => {
            const url = req.url || ''
            
            if (url.includes('/timeout')) {
                // Server sẽ không response để test timeout
                console.log('📡 Server nhận request timeout - không response')
                return
            }
            
            if (url.includes('/slow')) {
                // Server response chậm (2 giây)
                console.log('📡 Server nhận request slow - response sau 2s')
                setTimeout(() => {
                    res.writeHead(200, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ message: 'slow response' }))
                }, 2000)
                return
            }
            
            if (url.includes('/fast')) {
                // Server response nhanh
                console.log('📡 Server nhận request fast - response ngay')
                res.writeHead(200, { 'Content-Type': 'application/json' })
                res.end(JSON.stringify({ message: 'fast response' }))
                return
            }
            
            // Default response
            res.writeHead(404)
            res.end('Not Found')
        })

        return new Promise<void>((resolve) => {
            this.server!.listen(0, () => {
                this.port = (this.server!.address() as any)?.port || 0
                console.log(`✅ Test server đang chạy trên port ${this.port}`)
                resolve()
            })
        })
    }

    async cleanup() {
        if (this.server) {
            console.log('🧹 Đóng test server...')
            return new Promise<void>((resolve) => {
                this.server!.close(() => {
                    console.log('✅ Test server đã đóng')
                    resolve()
                })
            })
        }
    }

    getUrl(path: string) {
        return `http://localhost:${this.port}${path}`
    }

    async testTimeout() {
        console.log('\n🔥 TEST 1: Kiểm tra timeout')
        console.log('=' .repeat(50))
        
        const sender = new Sender(this.getUrl('/timeout'), { 
            timeout: 1000 // 1 giây timeout
        })

        const startTime = Date.now()
        
        try {
            await sender.send('test body')
            console.log('❌ FAIL: Request không bị timeout như mong đợi')
            return false
        } catch (error) {
            const duration = Date.now() - startTime
            console.log(`⏱️  Thời gian thực tế: ${duration}ms`)
            
            if (error instanceof SenderRequestError) {
                console.log(`🔍 Error type: ${error.constructor.name}`)
                console.log(`🔍 Error message: ${error.message}`)
                console.log(`🔍 Error cause: ${error.cause}`)
                
                // Kiểm tra xem có phải timeout error không
                const isTimeoutError = error.cause?.name === 'TimeoutError' || 
                                     error.message.includes('timeout') ||
                                     error.cause?.message?.includes('timeout')
                
                if (isTimeoutError && duration >= 900 && duration <= 1500) {
                    console.log('✅ PASS: Timeout hoạt động chính xác')
                    return true
                } else {
                    console.log('❌ FAIL: Timeout không hoạt động đúng')
                    return false
                }
            } else {
                console.log(`🔍 Unexpected error type: ${error}`)
                return false
            }
        }
    }

    async testAbortSignal() {
        console.log('\n🔥 TEST 2: Kiểm tra AbortSignal')
        console.log('=' .repeat(50))
        
        const sender = new Sender(this.getUrl('/slow'), { 
            timeout: 5000 // 5 giây timeout (dài hơn để test abort)
        })

        const abortController = new AbortController()
        const startTime = Date.now()
        
        // Abort sau 500ms
        setTimeout(() => {
            console.log('🛑 Gửi abort signal...')
            abortController.abort('Manual abort for testing')
        }, 500)

        try {
            await sender.send('test body', { 
                signal: abortController.signal 
            })
            console.log('❌ FAIL: Request không bị abort như mong đợi')
            return false
        } catch (error) {
            const duration = Date.now() - startTime
            console.log(`⏱️  Thời gian thực tế: ${duration}ms`)
            
            if (error instanceof SenderRequestError) {
                console.log(`🔍 Error type: ${error.constructor.name}`)
                console.log(`🔍 Error message: ${error.message}`)
                console.log(`🔍 Error cause: ${error.cause}`)
                
                // Kiểm tra xem có phải abort error không
                const isAbortError = error.cause?.name === 'AbortError' || 
                                   error.message.includes('abort') ||
                                   error.cause?.message?.includes('abort')
                
                if (isAbortError && duration >= 400 && duration <= 800) {
                    console.log('✅ PASS: AbortSignal hoạt động chính xác')
                    return true
                } else {
                    console.log('❌ FAIL: AbortSignal không hoạt động đúng')
                    return false
                }
            } else {
                console.log(`🔍 Unexpected error type: ${error}`)
                return false
            }
        }
    }

    async testTimeoutOverride() {
        console.log('\n🔥 TEST 3: Kiểm tra timeout override trong options')
        console.log('=' .repeat(50))
        
        const sender = new Sender(this.getUrl('/timeout'), { 
            timeout: 5000 // 5 giây default timeout
        })

        const startTime = Date.now()
        
        try {
            await sender.send('test body', { 
                timeout: 800 // Override thành 800ms
            })
            console.log('❌ FAIL: Request không bị timeout như mong đợi')
            return false
        } catch (error) {
            const duration = Date.now() - startTime
            console.log(`⏱️  Thời gian thực tế: ${duration}ms`)
            
            if (error instanceof SenderRequestError) {
                console.log(`🔍 Error type: ${error.constructor.name}`)
                console.log(`🔍 Error message: ${error.message}`)
                console.log(`🔍 Error cause: ${error.cause}`)
                
                const isTimeoutError = error.cause?.name === 'TimeoutError' || 
                                     error.message.includes('timeout') ||
                                     error.cause?.message?.includes('timeout')
                
                if (isTimeoutError && duration >= 700 && duration <= 1200) {
                    console.log('✅ PASS: Timeout override hoạt động chính xác')
                    return true
                } else {
                    console.log('❌ FAIL: Timeout override không hoạt động đúng')
                    return false
                }
            } else {
                console.log(`🔍 Unexpected error type: ${error}`)
                return false
            }
        }
    }

    async testSuccessfulRequest() {
        console.log('\n🔥 TEST 4: Kiểm tra request thành công (control test)')
        console.log('=' .repeat(50))
        
        const sender = new Sender(this.getUrl('/fast'), { 
            timeout: 2000
        })

        const startTime = Date.now()
        
        try {
            const response = await sender.send('test body')
            const duration = Date.now() - startTime
            
            console.log(`⏱️  Thời gian thực tế: ${duration}ms`)
            console.log(`🔍 Response status: ${response.status}`)
            console.log(`🔍 Response body: ${JSON.stringify(response.body)}`)
            
            if (response.status === 200 && duration < 1000) {
                console.log('✅ PASS: Request thành công hoạt động chính xác')
                return true
            } else {
                console.log('❌ FAIL: Request thành công không hoạt động đúng')
                return false
            }
        } catch (error) {
            console.log(`❌ FAIL: Request thành công bị lỗi: ${error}`)
            return false
        }
    }

    async runAllTests() {
        console.log('🧪 BẮT ĐẦU KIỂM TRA TIMEOUT VÀ ABORT CỦA SENDER CLASS')
        console.log('=' .repeat(60))
        
        await this.setup()
        
        const results = []
        
        try {
            results.push(await this.testTimeout())
            results.push(await this.testAbortSignal()) 
            results.push(await this.testTimeoutOverride())
            results.push(await this.testSuccessfulRequest())
        } finally {
            await this.cleanup()
        }
        
        console.log('\n📊 KẾT QUẢ TỔNG KẾT')
        console.log('=' .repeat(60))
        
        const passed = results.filter(r => r).length
        const total = results.length
        
        console.log(`✅ Passed: ${passed}/${total}`)
        console.log(`❌ Failed: ${total - passed}/${total}`)
        
        if (passed === total) {
            console.log('🎉 TẤT CẢ TEST ĐỀU PASS!')
        } else {
            console.log('⚠️  CÓ TEST BỊ FAIL!')
        }
        
        return passed === total
    }
}

// Chạy test
const testRunner = new TestRunner()
testRunner.runAllTests()
    .then((success) => {
        process.exit(success ? 0 : 1)
    })
    .catch((error) => {
        console.error('💥 Lỗi không mong đợi:', error)
        process.exit(1)
    })
